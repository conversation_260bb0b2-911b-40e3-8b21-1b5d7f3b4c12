    AREA    |.text|, CODE, READONLY
    THUMB
    REQUIRE8
    PRESERVE8

    ; 导入外部符号
    IMPORT  pCurrentTask
    IMPORT  pTopPriorityTask

    ; 导出函数
    EXPORT  SF_readyGo
    EXPORT  ZF_SR_Save
    EXPORT  ZF_SR_Restore
    EXPORT  INTERRUPT_DISABLE
    EXPORT  INTERRUPT_ENABLE
    EXPORT  PendSV_Handler

    ; 定义常量
NVIC_INT_CTRL       EQU     0xE000ED04
NVIC_SYSPRI14       EQU     0xE000ED22
NVIC_PENDSVSET      EQU     0x10000000
NVIC_PENDSV_PRI     EQU     0xFF

;******************************************************************************
; 描述  ：保存中断寄存器的值，然后关中断
; 参数  ：无
; 返回  ：中断寄存器的值
;******************************************************************************
ZF_SR_Save
    MRS     R0, PRIMASK
    CPSID   I
    BX      LR

;******************************************************************************
; 描述  ：恢复中断寄存器的值
; 参数  ：(in)-sr 中断寄存器的值
; 返回  ：无
;******************************************************************************
ZF_SR_Restore
    MSR     PRIMASK, R0
    BX      LR

;******************************************************************************
; 描述  ：关闭所有中断(但是不包括fault和NMI中断)
; 参数  ：无
; 返回  ：无
;******************************************************************************
INTERRUPT_DISABLE
    CPSID   I
    BX      LR

;******************************************************************************
; 描述  ：开启中断总开关
; 参数  ：无
; 返回  ：无
;******************************************************************************
INTERRUPT_ENABLE
    CPSIE   I
    BX      LR

;******************************************************************************
; 描述  ：开启中断，并触发PendSV异常(用于系统第一次任务调度)
; 参数  ：无
; 返回  ：无
;******************************************************************************
SF_readyGo
    ; 设置PendSV异常优先级为最低
    LDR     R0, =NVIC_SYSPRI14
    LDR     R1, =NVIC_PENDSV_PRI
    STRB    R1, [R0]
    NOP
    
    ; 设置psp的值为0，开始第一次上下文切换
    MOVS    R0, #0
    MSR     PSP, R0
    
    ; 触发PendSV异常
    LDR     R0, =NVIC_INT_CTRL
    LDR     R1, =NVIC_PENDSVSET
    STR     R1, [R0]
    
    ; 开中断
    CPSIE   I
    
; 一个死循环，程序正常运作不会来到这里
OSStartHang
    B       OSStartHang
    NOP

;******************************************************************************
; 描述  ：PendSV异常处理
; 参数  ：无
; 返回  ：无
;******************************************************************************
PendSV_Handler
    ; 任务的保存，即把CPU寄存器的值存储到任务的堆栈中
    ; 关中断，NMI和HardFault除外
    CPSID   I
    
    ; 判断是否第一次运行
    MRS     R0, PSP
    CBZ     R0, PendSV_Handler_Nosave
    
    ; 手动存储CPU寄存器R4-R11的值到当前任务的堆栈
    STMDB   R0!, {R4-R11}
    
    ; R0指向pCurrentTask的堆栈指针(指向空闲位置的顶部)
    LDR     R1, =pCurrentTask
    LDR     R1, [R1]
    STR     R0, [R1]

PendSV_Handler_Nosave
    ; 将pCurrentTask指向pTopPriorityTask
    LDR     R0, =pCurrentTask
    LDR     R1, =pTopPriorityTask
    LDR     R2, [R1]
    STR     R2, [R0]
    
    ; pTopPriorityTask的信息出栈
    LDR     R0, [R2]
    LDMIA   R0!, {R4-R11}
    
    ; 设置PSP指向下一个要执行的任务的堆栈的栈底(已弹出了寄存器信息)
    MSR     PSP, R0
    
    ; 确保异常返回使用的堆栈指针是PSP，即LR寄存器的位2要为1
    ORR     LR, LR, #0x04
    
    ; 开中断
    CPSIE   I
    
    ; 异常返回，这个时候任务堆栈中的剩下内容将会自动加载到CPU寄存器
    ; 同时PSP的值也将更新，即指向任务堆栈的栈顶
    BX      LR
    NOP

    END
