# IAR Embedded Workbench 中间文件和输出文件
# ==========================================

# IAR 编译输出目录
Debug/
Release/
Exe/
Obj/
List/

# IAR 项目设置文件 (用户特定)
*.dep
*.ewd
*.ewt
settings/

# IAR 编译中间文件
*.o
*.obj
*.d
*.lst
*.map
*.out
*.hex
*.bin
*.elf
*.axf

# IAR 调试文件
*.sim
*.jlink
*.dni
*.dnx
*.wsdt
*.dbgdt
*.crun
*.cspy.bat
*.cspy.ps1
*.driver.xcl
*.general.xcl

# IAR 备份文件
Backup*

# IAR 浏览信息
BrowseInfo/
*.pbi
*.pbd

# 通用编译输出
*.log
*.tmp
*.temp

# Windows 系统文件
Thumbs.db
Desktop.ini
*.lnk

# 编辑器临时文件
*~
*.swp
*.swo
.vscode/
*.code-workspace

# 其他常见中间文件
*.bak
*.orig
*.rej
*.patch

# 项目特定的输出目录
build/
output/
dist/

# 项目特定的临时文件
*.tmp
*.temp
*.bak
*.log
*.err
*.out
*.lst
*.map
*.hex
*.bin
*.elf
*.axf
*.obj
*.o
*.d
*.lst
*.map
*.out
*.hex
*.bin
*.elf
*.axf
*.obj
*.o
*.d
*.lst
*.map
*.out
*.txt