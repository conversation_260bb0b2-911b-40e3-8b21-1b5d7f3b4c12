###############################################################################
#
# IAR ELF Linker V9.40.1.364/W64 for ARM                  05/Oct/2025  17:00:09
# Copyright 2007-2023 IAR Systems AB.
#
#    Output file  =
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Exe\zorb_framework.out
#    Map file     =
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\List\zorb_framework.map
#    Command line =
#        -f
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Exe\zorb_framework.out.rsp
#        (D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\dma.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\gpio.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\i2c.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\main.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\spi.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\EWARM_18443280873093131863.dir\startup_stm32f103xe.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\stm32f1xx_hal_msp.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\stm32f1xx_it.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\CMSIS_6603591812247902717.dir\system_stm32f1xx.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir\usart.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_assert.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_buffer.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\ports_12060969804451139993.dir\zf_critical.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_event.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_fsm.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_list.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_task.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_task_schedule.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_time.o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir\zf_timer.o
#        --no_out_extension -o
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Exe\zorb_framework.out
#        --redirect _Printf=_PrintfFullNoMb --redirect _Scanf=_ScanfFullNoMb
#        --map
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\List\zorb_framework.map
#        --config
#        D:\routine\c_example\Zorb-Framework-master\EWARM/stm32f103xe_flash.icf
#        --semihosting --entry __iar_program_start --vfe --text_out locale
#        --cpu=Cortex-M3 --fpu=None) --dependencies=n
#        D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Exe\zorb_framework.out.iar_deps
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

CppFlavor       = *
__CPP_Runtime   = 1
__Heap_Handler  = Basic
__SystemLibrary = DLib
__dlib_version  = 6


*******************************************************************************
*** HEAP SELECTION
***

The basic heap was selected because --advanced_heap
was not specified and the application did not appear to
be primarily optimized for speed.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x800'0000 { ro section .intvec };
"P1":  place in [from 0x800'0000 to 0x807'ffff] { ro };
define block CSTACK with size = 8K, alignment = 8 { };
define block HEAP with size = 1K, alignment = 8 { };
"P2":  place in [from 0x2000'0000 to 0x2000'ffff] {
          rw, block CSTACK, block HEAP };
initialize by copy { rw };

  Section            Kind         Address    Size  Object
  -------            ----         -------    ----  ------
"A0":                                       0x130
  .intvec            ro code   0x800'0000   0x130  startup_stm32f103xe.o [3]
                             - 0x800'0130   0x130

"P1":                                      0x1e8c
  .text              ro code   0x800'0130   0x534  stm32f1xx_hal_uart.o [4]
  .text              ro code   0x800'0664   0x260  usart.o [2]
  .text              ro code   0x800'08c4   0x3b0  stm32f1xx_hal_dma.o [4]
  .text              ro code   0x800'0c74     0x2  stm32f1xx_hal_uart.o [4]
  .text              ro code   0x800'0c76     0x2  stm32f1xx_hal_uart.o [4]
  .text              ro code   0x800'0c78     0x2  stm32f1xx_hal_uart.o [4]
  .text              ro code   0x800'0c7a     0x2  stm32f1xx_hal_uart.o [4]
  .text              ro code   0x800'0c7c   0x4f2  stm32f1xx_hal_rcc.o [4]
  .text              ro code   0x800'116e    0x2a  copy_init3.o [9]
  .text              ro code   0x800'1198    0xa4  main.o [2]
  .text              ro code   0x800'123c    0x14  memset.o [9]
  .text              ro code   0x800'1250   0x1f4  stm32f1xx_hal_gpio.o [4]
  .text              ro code   0x800'1444    0x98  stm32f1xx_hal_cortex.o [4]
  .text              ro code   0x800'14dc     0xc  stm32f1xx_hal.o [4]
  .text              ro code   0x800'14e8    0x90  stm32f1xx_hal_timebase_tim.o [2]
  .text              ro code   0x800'1578    0x24  stm32f1xx_hal.o [4]
  .text              ro code   0x800'159c    0x7c  gpio.o [2]
  .text              ro code   0x800'1618    0x50  dma.o [2]
  .text              ro code   0x800'1668    0xb8  spi.o [2]
  .text              ro code   0x800'1720    0x9c  i2c.o [2]
  .text              ro code   0x800'17bc    0x10  stm32f1xx_hal.o [4]
  .text              ro code   0x800'17cc    0x66  ABImemset.o [9]
  .text              ro code   0x800'1834   0x294  stm32f1xx_hal_tim.o [4]
  .text              ro code   0x800'1ac8    0x44  stm32f1xx_hal_msp.o [2]
  .text              ro code   0x800'1b0c    0xbe  stm32f1xx_hal_spi.o [4]
  .text              ro code   0x800'1bcc   0x154  stm32f1xx_hal_i2c.o [4]
  .text              ro code   0x800'1d20     0x2  stm32f1xx_hal_tim.o [4]
  .text              ro code   0x800'1d22     0x2  stm32f1xx_hal_tim.o [4]
  .text              ro code   0x800'1d24     0x2  stm32f1xx_hal_tim.o [4]
  .text              ro code   0x800'1d26     0x2  stm32f1xx_hal_tim_ex.o [4]
  .text              ro code   0x800'1d28     0x2  stm32f1xx_hal_tim.o [4]
  .text              ro code   0x800'1d2a     0x2  stm32f1xx_hal_tim_ex.o [4]
  .text              ro code   0x800'1d2c     0x2  stm32f1xx_hal_tim.o [4]
  .text              ro code   0x800'1d30    0x58  stm32f1xx_it.o [2]
  .text              ro code   0x800'1d88    0x38  zero_init3.o [9]
  .text              ro code   0x800'1dc0    0x2c  iarttio.o [10]
  .text              ro code   0x800'1dec     0x8  XShttio.o [7]
  .text              ro code   0x800'1df4    0x28  data_init.o [9]
  .iar.init_table    const     0x800'1e1c    0x24  - Linker created -
  .text              ro code   0x800'1e40    0x1e  cmain.o [9]
  .text              ro code   0x800'1e5e     0x4  low_level_init.o [7]
  .text              ro code   0x800'1e62     0x4  exit.o [7]
  .text              ro code   0x800'1e68     0xa  cexit.o [9]
  .text              ro code   0x800'1e74    0x14  exit.o [10]
  .text              ro code   0x800'1e88    0x1c  cstartup_M.o [9]
  Initializer bytes  const     0x800'1ea4    0x18  <for P2-1>
  .text              ro code   0x800'1ebc    0x14  system_stm32f1xx.o [1]
  .text              ro code   0x800'1ed0    0x10  startup_stm32f103xe.o [3]
  .rodata            const     0x800'1ee0     0x8  system_stm32f1xx.o [1]
  .text              ro code   0x800'1ee8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1eec     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1ef0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1ef4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1ef8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1efc     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f00     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f04     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f08     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f0c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f10     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f14     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f18     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f1c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f20     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f24     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f28     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f2c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f30     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f34     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f38     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f3c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f40     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f44     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f48     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f4c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f50     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f54     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f58     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f5c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f60     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f64     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f68     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f6c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f70     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f74     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f78     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f7c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f80     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f84     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f88     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f8c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f90     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f94     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f98     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1f9c     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1fa0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1fa4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1fa8     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1fac     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1fb0     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1fb4     0x4  startup_stm32f103xe.o [3]
  .text              ro code   0x800'1fb8     0x4  startup_stm32f103xe.o [3]
  .rodata            const     0x800'1fbc     0x0  zero_init3.o [9]
  .rodata            const     0x800'1fbc     0x0  copy_init3.o [9]
                             - 0x800'1fbc  0x1e8c

"P2", part 1 of 3:                           0x18
  P2-1                        0x2000'0000    0x18  <Init block>
    .data            inited   0x2000'0000     0xc  stm32f1xx_hal.o [4]
    .data            inited   0x2000'000c     0x4  system_stm32f1xx.o [1]
    .data            inited   0x2000'0010     0x8  XShttio.o [7]
                            - 0x2000'0018    0x18

"P2", part 2 of 3:                          0x298
  .bss               zero     0x2000'0018    0xcc  usart.o [2]
  .bss               zero     0x2000'00e4    0x58  spi.o [2]
  .bss               zero     0x2000'013c    0x54  i2c.o [2]
  .bss               zero     0x2000'0190    0x48  stm32f1xx_hal_timebase_tim.o [2]
  .bss               zero     0x2000'01d8    0x48  usart.o [2]
  .bss               zero     0x2000'0220    0x48  usart.o [2]
  .bss               zero     0x2000'0268    0x48  usart.o [2]
                            - 0x2000'02b0   0x298

"P2", part 3 of 3:                         0x2000
  CSTACK                      0x2000'02b0  0x2000  <Block>
    CSTACK           uninit   0x2000'02b0  0x2000  <Block tail>
                            - 0x2000'22b0  0x2000

Unused ranges:

         From           To      Size
         ----           --      ----
   0x800'1fbc   0x807'ffff  0x7'e044
  0x2000'22b0  0x2000'ffff    0xdd50


*******************************************************************************
*** INIT TABLE
***

          Address      Size
          -------      ----
Zero (__iar_zero_init3)
    1 destination range, total size 0x298:
          0x2000'0018  0x298

Copy (__iar_copy_init3)
    1 source range, total size 0x18:
           0x800'1ea4   0x18
    1 destination range, total size 0x18:
          0x2000'0000   0x18



*******************************************************************************
*** MODULE SUMMARY
***

    Module                        ro code  ro data  rw data
    ------                        -------  -------  -------
command line/config:
    -------------------------------------------------------
    Total:

D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\CMSIS_6603591812247902717.dir: [1]
    system_stm32f1xx.o                 20       12        4
    -------------------------------------------------------
    Total:                             20       12        4

D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir: [2]
    dma.o                              80
    gpio.o                            124
    i2c.o                             156                84
    main.o                            164
    spi.o                             184                88
    stm32f1xx_hal_msp.o                68
    stm32f1xx_hal_timebase_tim.o      144                72
    stm32f1xx_it.o                     88
    usart.o                           608               420
    -------------------------------------------------------
    Total:                          1'616               664

D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\EWARM_18443280873093131863.dir: [3]
    startup_stm32f103xe.o             532
    -------------------------------------------------------
    Total:                            532

D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir: [4]
    stm32f1xx_hal.o                    64       12       12
    stm32f1xx_hal_cortex.o            152
    stm32f1xx_hal_dma.o               944
    stm32f1xx_hal_gpio.o              500
    stm32f1xx_hal_i2c.o               340
    stm32f1xx_hal_rcc.o             1'266
    stm32f1xx_hal_spi.o               190
    stm32f1xx_hal_tim.o               670
    stm32f1xx_hal_tim_ex.o              4
    stm32f1xx_hal_uart.o            1'340
    -------------------------------------------------------
    Total:                          5'470       12       12

D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir: [5]
    -------------------------------------------------------
    Total:

D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\ports_12060969804451139993.dir: [6]
    -------------------------------------------------------
    Total:

dl7M_tlf.a: [7]
    XShttio.o                           8        8        8
    exit.o                              4
    low_level_init.o                    4
    -------------------------------------------------------
    Total:                             16        8        8

m7M_tl.a: [8]
    -------------------------------------------------------
    Total:

rt7M_tl.a: [9]
    ABImemset.o                       102
    cexit.o                            10
    cmain.o                            30
    copy_init3.o                       42
    cstartup_M.o                       28
    data_init.o                        40
    memset.o                           20
    zero_init3.o                       56
    -------------------------------------------------------
    Total:                            328

shb_l.a: [10]
    exit.o                             20
    iarttio.o                          44
    -------------------------------------------------------
    Total:                             64

    Gaps                               10
    Linker created                              36    8'192
-----------------------------------------------------------
    Grand Total:                    8'056       68    8'880


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address   Size  Type      Object
-----                       -------   ----  ----      ------
.iar.init_table$$Base    0x800'1e1c          --   Gb  - Linker created -
.iar.init_table$$Limit   0x800'1e40          --   Gb  - Linker created -
?main                    0x800'1e41         Code  Gb  cmain.o [9]
ADC1_2_IRQHandler        0x800'1f25         Code  Wk  startup_stm32f103xe.o [3]
ADC3_IRQHandler          0x800'1f89         Code  Wk  startup_stm32f103xe.o [3]
AHBPrescTable            0x800'1ec0   0x10  Data  Gb  system_stm32f1xx.o [1]
APBPrescTable            0x800'1ee0    0x8  Data  Gb  system_stm32f1xx.o [1]
BusFault_Handler         0x800'1d37    0x2  Code  Gb  stm32f1xx_it.o [2]
CAN1_RX1_IRQHandler      0x800'1f31         Code  Wk  startup_stm32f103xe.o [3]
CAN1_SCE_IRQHandler      0x800'1f35         Code  Wk  startup_stm32f103xe.o [3]
CSTACK$$Base            0x2000'02b0          --   Gb  - Linker created -
CSTACK$$Limit           0x2000'22b0          --   Gb  - Linker created -
DMA1_Channel1_IRQHandler
                         0x800'1f15         Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel2_IRQHandler
                         0x800'1f19         Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel3_IRQHandler
                         0x800'1d43    0x6  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel4_IRQHandler
                         0x800'1f1d         Code  Wk  startup_stm32f103xe.o [3]
DMA1_Channel5_IRQHandler
                         0x800'1d49    0x6  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel6_IRQHandler
                         0x800'1d4f    0x6  Code  Gb  stm32f1xx_it.o [2]
DMA1_Channel7_IRQHandler
                         0x800'1f21         Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel1_IRQHandler
                         0x800'1fad         Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel2_IRQHandler
                         0x800'1fb1         Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel3_IRQHandler
                         0x800'1fb5         Code  Wk  startup_stm32f103xe.o [3]
DMA2_Channel4_5_IRQHandler
                         0x800'1fb9         Code  Wk  startup_stm32f103xe.o [3]
DebugMon_Handler         0x800'1d3d    0x2  Code  Gb  stm32f1xx_it.o [2]
EXTI0_IRQHandler         0x800'1f01         Code  Wk  startup_stm32f103xe.o [3]
EXTI15_10_IRQHandler     0x800'1f6d         Code  Wk  startup_stm32f103xe.o [3]
EXTI1_IRQHandler         0x800'1f05         Code  Wk  startup_stm32f103xe.o [3]
EXTI2_IRQHandler         0x800'1f09         Code  Wk  startup_stm32f103xe.o [3]
EXTI3_IRQHandler         0x800'1f0d         Code  Wk  startup_stm32f103xe.o [3]
EXTI4_IRQHandler         0x800'1f11         Code  Wk  startup_stm32f103xe.o [3]
EXTI9_5_IRQHandler       0x800'1f39         Code  Wk  startup_stm32f103xe.o [3]
Error_Handler            0x800'1239    0x4  Code  Gb  main.o [2]
FLASH_IRQHandler         0x800'1ef9         Code  Wk  startup_stm32f103xe.o [3]
FSMC_IRQHandler          0x800'1f8d         Code  Wk  startup_stm32f103xe.o [3]
HAL_DMA_Abort            0x800'0949   0x4a  Code  Gb  stm32f1xx_hal_dma.o [4]
HAL_DMA_Abort_IT         0x800'0993   0xdc  Code  Gb  stm32f1xx_hal_dma.o [4]
HAL_DMA_IRQHandler       0x800'0a6f  0x1be  Code  Gb  stm32f1xx_hal_dma.o [4]
HAL_DMA_Init             0x800'08c5   0x84  Code  Gb  stm32f1xx_hal_dma.o [4]
HAL_GPIO_Init            0x800'1251  0x1b0  Code  Gb  stm32f1xx_hal_gpio.o [4]
HAL_GPIO_WritePin        0x800'1401    0x8  Code  Gb  stm32f1xx_hal_gpio.o [4]
HAL_GetTick              0x800'14dd    0xc  Code  Wk  stm32f1xx_hal.o [4]
HAL_I2C_Init             0x800'1bcd  0x12e  Code  Gb  stm32f1xx_hal_i2c.o [4]
HAL_I2C_MspInit          0x800'1751   0x56  Code  Gb  i2c.o [2]
HAL_IncTick              0x800'17bd   0x10  Code  Wk  stm32f1xx_hal.o [4]
HAL_Init                 0x800'1579   0x20  Code  Gb  stm32f1xx_hal.o [4]
HAL_InitTick             0x800'14e9   0x7a  Code  Gb  stm32f1xx_hal_timebase_tim.o [2]
HAL_MspInit              0x800'1ac9   0x44  Code  Gb  stm32f1xx_hal_msp.o [2]
HAL_NVIC_EnableIRQ       0x800'14b3   0x16  Code  Gb  stm32f1xx_hal_cortex.o [4]
HAL_NVIC_SetPriority     0x800'1479   0x3a  Code  Gb  stm32f1xx_hal_cortex.o [4]
HAL_NVIC_SetPriorityGrouping
                         0x800'145f   0x1a  Code  Gb  stm32f1xx_hal_cortex.o [4]
HAL_RCC_ClockConfig      0x800'0f91   0xf4  Code  Gb  stm32f1xx_hal_rcc.o [4]
HAL_RCC_GetClockConfig   0x800'10f5   0x36  Code  Gb  stm32f1xx_hal_rcc.o [4]
HAL_RCC_GetPCLK1Freq     0x800'10d1   0x10  Code  Gb  stm32f1xx_hal_rcc.o [4]
HAL_RCC_GetPCLK2Freq     0x800'10e1    0xe  Code  Gb  stm32f1xx_hal_rcc.o [4]
HAL_RCC_GetSysClockFreq
                         0x800'1099   0x38  Code  Gb  stm32f1xx_hal_rcc.o [4]
HAL_RCC_GetSysClockFreq::aPLLMULFactorTable
                         0x800'115c   0x10  Data  Lc  stm32f1xx_hal_rcc.o [4]
HAL_RCC_GetSysClockFreq::aPredivFactorTable
                         0x800'116c    0x2  Data  Lc  stm32f1xx_hal_rcc.o [4]
HAL_RCC_OscConfig        0x800'0c7d  0x30c  Code  Gb  stm32f1xx_hal_rcc.o [4]
HAL_SPI_Init             0x800'1b0d   0xaa  Code  Gb  stm32f1xx_hal_spi.o [4]
HAL_SPI_MspInit          0x800'16a3   0x6e  Code  Gb  spi.o [2]
HAL_TIMEx_BreakCallback
                         0x800'1d27    0x2  Code  Wk  stm32f1xx_hal_tim_ex.o [4]
HAL_TIMEx_CommutCallback
                         0x800'1d2b    0x2  Code  Wk  stm32f1xx_hal_tim_ex.o [4]
HAL_TIM_Base_Init        0x800'1835   0x1a  Code  Gb  stm32f1xx_hal_tim.o [4]
HAL_TIM_Base_MspInit     0x800'1d21    0x2  Code  Wk  stm32f1xx_hal_tim.o [4]
HAL_TIM_Base_Start_IT    0x800'184f   0x66  Code  Gb  stm32f1xx_hal_tim.o [4]
HAL_TIM_IC_CaptureCallback
                         0x800'1d23    0x2  Code  Wk  stm32f1xx_hal_tim.o [4]
HAL_TIM_IRQHandler       0x800'18f9  0x12a  Code  Gb  stm32f1xx_hal_tim.o [4]
HAL_TIM_OC_DelayElapsedCallback
                         0x800'1d25    0x2  Code  Wk  stm32f1xx_hal_tim.o [4]
HAL_TIM_PWM_PulseFinishedCallback
                         0x800'1d2d    0x2  Code  Wk  stm32f1xx_hal_tim.o [4]
HAL_TIM_PeriodElapsedCallback
                         0x800'1225    0xe  Code  Gb  main.o [2]
HAL_TIM_TriggerCallback
                         0x800'1d29    0x2  Code  Wk  stm32f1xx_hal_tim.o [4]
HAL_UARTEx_RxEventCallback
                         0x800'0c77    0x2  Code  Wk  stm32f1xx_hal_uart.o [4]
HAL_UART_ErrorCallback   0x800'0c75    0x2  Code  Wk  stm32f1xx_hal_uart.o [4]
HAL_UART_IRQHandler      0x800'01cb  0x28e  Code  Gb  stm32f1xx_hal_uart.o [4]
HAL_UART_Init            0x800'0131   0x1e  Code  Gb  stm32f1xx_hal_uart.o [4]
HAL_UART_MspInit         0x800'06d5  0x17a  Code  Gb  usart.o [2]
HAL_UART_RxCpltCallback
                         0x800'0c7b    0x2  Code  Wk  stm32f1xx_hal_uart.o [4]
HAL_UART_TxCpltCallback
                         0x800'0c79    0x2  Code  Wk  stm32f1xx_hal_uart.o [4]
HardFault_Handler        0x800'1d33    0x2  Code  Gb  stm32f1xx_it.o [2]
I2C1_ER_IRQHandler       0x800'1f59         Code  Wk  startup_stm32f103xe.o [3]
I2C1_EV_IRQHandler       0x800'1f55         Code  Wk  startup_stm32f103xe.o [3]
I2C2_ER_IRQHandler       0x800'1f61         Code  Wk  startup_stm32f103xe.o [3]
I2C2_EV_IRQHandler       0x800'1f5d         Code  Wk  startup_stm32f103xe.o [3]
MX_DMA_Init              0x800'1619   0x50  Code  Gb  dma.o [2]
MX_GPIO_Init             0x800'159d   0x7c  Code  Gb  gpio.o [2]
MX_I2C1_Init             0x800'1721   0x30  Code  Gb  i2c.o [2]
MX_SPI2_Init             0x800'1669   0x3a  Code  Gb  spi.o [2]
MX_USART1_UART_Init      0x800'0665   0x2c  Code  Gb  usart.o [2]
MX_USART2_UART_Init      0x800'0691   0x10  Code  Gb  usart.o [2]
MX_USART3_UART_Init      0x800'06a9   0x10  Code  Gb  usart.o [2]
MemManage_Handler        0x800'1d35    0x2  Code  Gb  stm32f1xx_it.o [2]
NMI_Handler              0x800'1d31    0x2  Code  Gb  stm32f1xx_it.o [2]
PVD_IRQHandler           0x800'1eed         Code  Wk  startup_stm32f103xe.o [3]
PendSV_Handler           0x800'1d3f    0x2  Code  Gb  stm32f1xx_it.o [2]
RCC_IRQHandler           0x800'1efd         Code  Wk  startup_stm32f103xe.o [3]
RTC_Alarm_IRQHandler     0x800'1f71         Code  Wk  startup_stm32f103xe.o [3]
RTC_IRQHandler           0x800'1ef5         Code  Wk  startup_stm32f103xe.o [3]
Region$$Table$$Base      0x800'1e1c          --   Gb  - Linker created -
Region$$Table$$Limit     0x800'1e40          --   Gb  - Linker created -
Reset_Handler            0x800'1ed1         Code  Wk  startup_stm32f103xe.o [3]
SDIO_IRQHandler          0x800'1f91         Code  Wk  startup_stm32f103xe.o [3]
SPI1_IRQHandler          0x800'1f65         Code  Wk  startup_stm32f103xe.o [3]
SPI2_IRQHandler          0x800'1f69         Code  Wk  startup_stm32f103xe.o [3]
SPI3_IRQHandler          0x800'1f99         Code  Wk  startup_stm32f103xe.o [3]
SVC_Handler              0x800'1d3b    0x2  Code  Gb  stm32f1xx_it.o [2]
SysTick_Handler          0x800'1d41    0x2  Code  Gb  stm32f1xx_it.o [2]
SystemClock_Config       0x800'11c1   0x64  Code  Gb  main.o [2]
SystemCoreClock         0x2000'000c    0x4  Data  Gb  system_stm32f1xx.o [1]
SystemInit               0x800'1ebd    0x2  Code  Gb  system_stm32f1xx.o [1]
TAMPER_IRQHandler        0x800'1ef1         Code  Wk  startup_stm32f103xe.o [3]
TIM1_BRK_IRQHandler      0x800'1f3d         Code  Wk  startup_stm32f103xe.o [3]
TIM1_CC_IRQHandler       0x800'1f45         Code  Wk  startup_stm32f103xe.o [3]
TIM1_TRG_COM_IRQHandler
                         0x800'1f41         Code  Wk  startup_stm32f103xe.o [3]
TIM1_UP_IRQHandler       0x800'1d55    0x6  Code  Gb  stm32f1xx_it.o [2]
TIM2_IRQHandler          0x800'1f49         Code  Wk  startup_stm32f103xe.o [3]
TIM3_IRQHandler          0x800'1f4d         Code  Wk  startup_stm32f103xe.o [3]
TIM4_IRQHandler          0x800'1f51         Code  Wk  startup_stm32f103xe.o [3]
TIM5_IRQHandler          0x800'1f95         Code  Wk  startup_stm32f103xe.o [3]
TIM6_IRQHandler          0x800'1fa5         Code  Wk  startup_stm32f103xe.o [3]
TIM7_IRQHandler          0x800'1fa9         Code  Wk  startup_stm32f103xe.o [3]
TIM8_BRK_IRQHandler      0x800'1f79         Code  Wk  startup_stm32f103xe.o [3]
TIM8_CC_IRQHandler       0x800'1f85         Code  Wk  startup_stm32f103xe.o [3]
TIM8_TRG_COM_IRQHandler
                         0x800'1f81         Code  Wk  startup_stm32f103xe.o [3]
TIM8_UP_IRQHandler       0x800'1f7d         Code  Wk  startup_stm32f103xe.o [3]
TIM_Base_SetConfig       0x800'1a31   0x82  Code  Gb  stm32f1xx_hal_tim.o [4]
UART4_IRQHandler         0x800'1f9d         Code  Wk  startup_stm32f103xe.o [3]
UART5_IRQHandler         0x800'1fa1         Code  Wk  startup_stm32f103xe.o [3]
UART_DMAAbortOnError     0x800'0501    0xe  Code  Lc  stm32f1xx_hal_uart.o [4]
UART_EndRxTransfer       0x800'04bd   0x36  Code  Lc  stm32f1xx_hal_uart.o [4]
UART_Receive_IT          0x800'0511   0xb4  Code  Lc  stm32f1xx_hal_uart.o [4]
UART_SetConfig           0x800'05e1   0x78  Code  Lc  stm32f1xx_hal_uart.o [4]
USART1_IRQHandler        0x800'1d5b    0x6  Code  Gb  stm32f1xx_it.o [2]
USART2_IRQHandler        0x800'1d61    0x6  Code  Gb  stm32f1xx_it.o [2]
USART3_IRQHandler        0x800'1d67    0x6  Code  Gb  stm32f1xx_it.o [2]
USBWakeUp_IRQHandler     0x800'1f75         Code  Wk  startup_stm32f103xe.o [3]
USB_HP_CAN1_TX_IRQHandler
                         0x800'1f29         Code  Wk  startup_stm32f103xe.o [3]
USB_LP_CAN1_RX0_IRQHandler
                         0x800'1f2d         Code  Wk  startup_stm32f103xe.o [3]
UsageFault_Handler       0x800'1d39    0x2  Code  Gb  stm32f1xx_it.o [2]
WWDG_IRQHandler          0x800'1ee9         Code  Wk  startup_stm32f103xe.o [3]
__NVIC_SetPriority       0x800'1445   0x1a  Code  Lc  stm32f1xx_hal_cortex.o [4]
__aeabi_memset           0x800'17cd         Code  Gb  ABImemset.o [9]
__cmain                  0x800'1e41         Code  Gb  cmain.o [9]
__exit                   0x800'1e75   0x14  Code  Gb  exit.o [10]
__iar_Memset             0x800'17cd         Code  Gb  ABImemset.o [9]
__iar_Memset_word        0x800'17d5         Code  Gb  ABImemset.o [9]
__iar_close_ttio         0x800'1dc1   0x2c  Code  Gb  iarttio.o [10]
__iar_copy_init3         0x800'116f   0x2a  Code  Gb  copy_init3.o [9]
__iar_data_init3         0x800'1df5   0x28  Code  Gb  data_init.o [9]
__iar_lookup_ttioh       0x800'1ded    0x8  Code  Gb  XShttio.o [7]
__iar_program_start      0x800'1e89         Code  Gb  cstartup_M.o [9]
__iar_ttio_handles      0x2000'0010    0x8  Data  Lc  XShttio.o [7]
__iar_zero_init3         0x800'1d89   0x38  Code  Gb  zero_init3.o [9]
__low_level_init         0x800'1e5f    0x4  Code  Gb  low_level_init.o [7]
__vector_table           0x800'0000         Data  Gb  startup_stm32f103xe.o [3]
_call_main               0x800'1e4d         Code  Gb  cmain.o [9]
_exit                    0x800'1e69         Code  Gb  cexit.o [9]
exit                     0x800'1e63    0x4  Code  Gb  exit.o [7]
hdma_usart1_rx          0x2000'0018   0x44  Data  Gb  usart.o [2]
hdma_usart2_rx          0x2000'005c   0x44  Data  Gb  usart.o [2]
hdma_usart3_rx          0x2000'00a0   0x44  Data  Gb  usart.o [2]
hi2c1                   0x2000'013c   0x54  Data  Gb  i2c.o [2]
hspi2                   0x2000'00e4   0x58  Data  Gb  spi.o [2]
htim1                   0x2000'0190   0x48  Data  Gb  stm32f1xx_hal_timebase_tim.o [2]
huart1                  0x2000'01d8   0x48  Data  Gb  usart.o [2]
huart2                  0x2000'0220   0x48  Data  Gb  usart.o [2]
huart3                  0x2000'0268   0x48  Data  Gb  usart.o [2]
main                     0x800'1199   0x28  Code  Gb  main.o [2]
memset                   0x800'123d   0x14  Code  Gb  memset.o [9]
uwTick                  0x2000'0004    0x4  Data  Gb  stm32f1xx_hal.o [4]
uwTickFreq              0x2000'0000    0x1  Data  Gb  stm32f1xx_hal.o [4]
uwTickPrio              0x2000'0008    0x4  Data  Gb  stm32f1xx_hal.o [4]


[1] = D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\CMSIS_6603591812247902717.dir
[2] = D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir
[3] = D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\EWARM_18443280873093131863.dir
[4] = D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir
[5] = D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir
[6] = D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\ports_12060969804451139993.dir
[7] = dl7M_tlf.a
[8] = m7M_tl.a
[9] = rt7M_tl.a
[10] = shb_l.a

  8'056 bytes of readonly  code memory
     68 bytes of readonly  data memory
  8'880 bytes of readwrite data memory

Errors: none
Warnings: none
