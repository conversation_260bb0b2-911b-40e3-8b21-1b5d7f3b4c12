##IAR Ninja build file


#Rules
rule COMPILER_XCL
  command = D$:\software\iar\common\bin\XclFileGenerator.exe $xclcommand -f "$rspfile_name"
  description = IAR_NEW_TOOL+++COMPILER_XCL+++$out
  rspfile = $rspfile_name
  rspfile_content = $flags

rule INDEXER
  command = D$:\software\iar\common\bin\SourceIndexer.exe $flags
  depfile = $out.dep
  deps = gcc
  description = IAR_NEW_TOOL+++INDEXER+++$out

rule MAKEBROWSE
  command = D$:\software\iar\common\bin\makeBrowseData.exe $flags
  description = IAR_NEW_TOOL+++MAKEBROWSE+++$out

rule PDBLINK
  command = D$:\software\iar\common\bin\PbdLink.exe $flags
  description = IAR_NEW_TOOL+++PDBLINK+++$out



#Build steps
build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\dma.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\dma.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\gpio.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\gpio.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\i2c.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\i2c.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\main.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\main.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\spi.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\spi.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_hal_msp.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_hal_msp.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_hal_timebase_tim.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_hal_timebase_tim.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_it.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_it.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\usart.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\Core_13247989168731456611.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\usart.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Core\Src\system_stm32f1xx.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\CMSIS_6603591812247902717.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Core\Src\system_stm32f1xx.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\STM32F1xx_HAL_Driver_9701002599162248031.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_assert.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_assert.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_buffer.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_buffer.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_event.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_event.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_fsm.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_fsm.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_list.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_list.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_task.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_task.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_task_schedule.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_task_schedule.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_time.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_time.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_timer.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_timer.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.xcl : COMPILER_XCL 
    flags = D$:\routine\c_example\Zorb-Framework-master\zorb_framework\ports\zf_critical.c -D USE_HAL_DRIVER -D STM32F103xE -o D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\ports_12060969804451139993.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D$:\software\iar\arm\inc\c\DLib_Config_Full.h -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D$:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz --predef_macros D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.tmp
    rspfile_name = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.xcl.rsp
    xclcommand = -source_file D$:\routine\c_example\Zorb-Framework-master\zorb_framework\ports\zf_critical.c -xcl_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.xcl -macro_file D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.tmp -icc_path D$:\software\iar\arm\bin\iccarm.exe

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\dma.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\gpio.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\i2c.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\main.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\spi.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_hal_msp.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_hal_timebase_tim.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\stm32f1xx_it.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\usart.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl | D$:\routine\c_example\Zorb-Framework-master\Core\Src\system_stm32f1xx.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl | D$:\routine\c_example\Zorb-Framework-master\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_assert.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_buffer.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_event.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_fsm.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_list.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_task.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_task_schedule.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_time.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_timer.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.pbi : INDEXER D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.xcl | D$:\routine\c_example\Zorb-Framework-master\zorb_framework\ports\zf_critical.c
    flags = -out=D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.pbi -f D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.xcl

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework.pbw : MAKEBROWSE D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework.pbd
    flags = D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework.pbd -output D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework.pbw

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part0.pbi : PDBLINK D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.pbi | D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.pbi
    flags = -M D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part0.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\dma.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\gpio.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\i2c.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\main.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\spi.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_msp.pbi

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part1.pbi : PDBLINK D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.pbi | D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.pbi
    flags = -M D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part1.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_hal_timebase_tim.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\stm32f1xx_it.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\Core_13247989168731456611.dir\usart.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\CMSIS_6603591812247902717.dir\system_stm32f1xx.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_cortex.pbi

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part2.pbi : PDBLINK D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.pbi | D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.pbi
    flags = -M D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part2.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_dma.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_exti.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_flash_ex.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_gpio_ex.pbi

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part3.pbi : PDBLINK D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.pbi | D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.pbi
    flags = -M D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part3.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_i2c.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_pwr.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_rcc_ex.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_spi.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim.pbi

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part4.pbi : PDBLINK D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.pbi | D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.pbi
    flags = -M D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part4.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_tim_ex.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\STM32F1xx_HAL_Driver_9701002599162248031.dir\stm32f1xx_hal_uart.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_assert.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_buffer.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_event.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_fsm.pbi

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part5.pbi : PDBLINK D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.pbi | D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.pbi
    flags = -M D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part5.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_list.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_task_schedule.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_time.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\framework_14694697249995373192.dir\zf_timer.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\ports_12060969804451139993.dir\zf_critical.pbi

build D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework.pbd : PDBLINK D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part0.pbi | D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part1.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part2.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part3.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part4.pbi $
 D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part5.pbi
    flags = -M D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework.pbd D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part0.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part1.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part2.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part3.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part4.pbi D$:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\BrowseInfo\zorb_framework_part5.pbi

