﻿D:\routine\c_example\Zorb-Framework-master\zorb_framework\src\zf_event.c -D USE_HAL_DRIVER -D STM32F103xE -o D:\routine\c_example\Zorb-Framework-master\EWARM\zorb_framework\Obj\framework_14694697249995373192.dir --debug --endian=little --cpu=Cortex-M3 -e --fpu=None --dlib_config D:\software\iar\arm\inc\c\DLib_Config_Full.h -I D:\routine\c_example\Zorb-Framework-master\EWARM/../Core/Inc\ -I D:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc\ -I D:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy\ -I D:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Device/ST/STM32F1xx/Include\ -I D:\routine\c_example\Zorb-Framework-master\EWARM/../Drivers/CMSIS/Include\ -I D:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/inc\ -I D:\routine\c_example\Zorb-Framework-master\EWARM/../zorb_framework/ports\ -Ohz