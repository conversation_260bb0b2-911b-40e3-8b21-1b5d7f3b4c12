/**
  *****************************************************************************
  * @file    zf_critical.c
  * <AUTHOR>
  * @version V1.0.0
  * @date    2018-06-28
  * @brief   硬件相关操作(临界状态、现场保护与恢复、PendSV异常)的实现
  *****************************************************************************
  * @history
  *
  * 1. Date:2018-06-28
  *    Author:Zorb
  *    Modification:建立文件
  *
  *****************************************************************************
  */

#include "zf_critical.h"
#include "zf_assert.h"

// 汇编函数在 zf_critical_asm.s 文件中实现
// 这里只保留外部声明，实际实现在汇编文件中

/******************************************************************************
 * 描述  ：初始化任务堆栈
 * 参数  ：(in)-pTask       任务指针
 *         (in)-taskProcess 任务程序指针
 *         (in)-parg        任务程序参数列表指针
 * 返回  ：无
******************************************************************************/
void ZF_initTaskStack(Task *pTask, ITaskProcess taskProcess, void *parg)
{
    ZF_STK_TYPE *pStkPtr;
    
    ZF_ASSERT(pTask != (Task *)0)
    ZF_ASSERT(taskProcess != (ITaskProcess)0)
    
    pStkPtr = (ZF_STK_TYPE *)((ZF_STK_TYPE)pTask->pStkBase
        + (ZF_STK_TYPE)pTask->StkSize);
    
    /* 异常发生时自动保存的寄存器 */
    *--pStkPtr = (ZF_STK_TYPE)0x01000000u;          /* xPSR的bit24必须置1 */
    *--pStkPtr = (ZF_STK_TYPE)taskProcess;          /* 任务的入口地址 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R14 (LR) */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R12 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R3 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R2 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R1 */
    *--pStkPtr = (ZF_STK_TYPE)parg;                 /* R0 : 任务形参 */
    
    /* 异常发生时需手动保存的寄存器 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R11 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R10 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R9 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R8 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R7 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R6 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R5 */
    *--pStkPtr = (ZF_STK_TYPE)0x00000000u;          /* R4 */
    
    pTask->pStkPtr = pStkPtr;
}

// SF_readyGo 函数在 zf_critical_asm.s 文件中实现

// PendSV_Handler 函数在 zf_critical_asm.s 文件中实现

/******************************** END OF FILE ********************************/
