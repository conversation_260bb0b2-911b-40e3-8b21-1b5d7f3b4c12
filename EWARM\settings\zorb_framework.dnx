<?xml version="1.0"?>
<settings>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <PlDriver>
        <MemConfigValue>D:\software\iar\arm\config\debugger\ST\STM32F103ZE.ddf</MemConfigValue>
    </PlDriver>
    <StLinkDriver>
        <LeaveTargetRunning>_ 0</LeaveTargetRunning>
        <CStepIntDis>_ 0</CStepIntDis>
    </StLinkDriver>
    <ArmDriver>
        <EnableCache>0</EnableCache>
        <EnforceMemoryConfiguration>1</EnforceMemoryConfiguration>
    </ArmDriver>
    <DisassembleMode>
        <mode>0</mode>
    </DisassembleMode>
    <Breakpoints2>
        <Count>0</Count>
    </Breakpoints2>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
</settings>
